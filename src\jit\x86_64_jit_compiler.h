#pragma once

#include <cstdint>
#include <functional>
#include <istream>
#include <memory>
#include <mutex>
#include <ostream>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "cpu/decoded_instruction.h"

namespace x86_64 {
class X86_64CPU; // Forward declaration

/**
 * @brief JIT compiler for x86_64 instructions.
 */
class X86_64JITCompiler {
public:
  /**
   * @brief Constructs the JIT compiler.
   * @param cpu Pointer to the associated X86_64CPU instance.
   */
  explicit X86_64JITCompiler(X86_64CPU *cpu);

  /**
   * @brief Destructs the JIT compiler, cleaning up allocated resources.
   */
  ~X86_64JITCompiler() noexcept;

  /**
   * @brief Compiles a basic block starting at the given program counter.
   * @param pc Starting program counter.
   * @return True on successful compilation, false otherwise.
   */
  bool CompileBlock(uint64_t pc);

  /**
   * @brief Executes compiled code at the given program counter.
   * @param pc Starting program counter.
   * @return True if executed, false if no compiled code exists.
   */
  bool ExecuteCompiledCode(uint64_t pc);

  /**
   * @brief Clears the entire JIT cache.
   */
  void ClearCache();

  /**
   * @brief Invalidates a compiled block at the given address.
   * @param pc Block address.
   */
  void InvalidateBlock(uint64_t pc);

  /**
   * @brief Invalidates compiled blocks in a memory range.
   * @param addr Starting address.
   * @param size Size of the range.
   */
  void InvalidateRange(uint64_t addr, size_t size);

  /**
   * @brief Gets the cache usage ratio.
   * @return Usage ratio (0.0 to 1.0).
   */
  float GetCacheUsage() const;

  /**
   * @brief Statistics for JIT operations.
   */
  struct Stats {
    uint64_t blocksCompiled = 0;   ///< Number of blocks compiled
    uint64_t executions = 0;       ///< Number of block executions
    uint64_t cacheClears = 0;      ///< Number of cache clears
    uint64_t simdInstructions = 0; ///< Number of SIMD instructions compiled
    uint64_t compilationLatencyUs =
        0;                    ///< Total compilation latency in microseconds
    uint64_t cacheHits = 0;   ///< Number of cache hits
    uint64_t cacheMisses = 0; ///< Number of cache misses
  };

  /**
   * @brief Retrieves JIT statistics.
   * @return Compilation and execution stats.
   */
  Stats GetStats() const { return m_stats; }

  /**
   * @brief Saves the JIT cache state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the JIT cache state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Profiles a block to identify hot code paths.
   * @param pc Block address.
   * @param cycles Cycles spent in the block.
   */
  void ProfileBlock(uint64_t pc, uint64_t cycles);

  /**
   * @brief Retrieves the top N hot blocks for optimization reports.
   * @param count Number of hot blocks to retrieve.
   * @return Vector of hot blocks and their execution counts.
   */
  std::vector<std::pair<uint64_t, uint32_t>>
  GetHotBlocks(size_t count = 10) const;

  /**
   * @brief Enables or disables tiered compilation for performance optimization.
   * @param enable True to enable, false to disable.
   */
  void EnableTieredCompilation(bool enable) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    m_tieredCompilationEnabled = enable;
  }

  /**
   * @brief Enables or disables SIMD optimizations.
   * @param enable True to enable, false to disable.
   */
  void EnableSIMDOptimizations(bool enable) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    m_simdOptimizationsEnabled = enable;
  }

  /**
   * @brief Recompiles a hot block with higher optimization levels.
   * @param pc Block address.
   * @param optimizationLevel Optimization level to apply.
   * @return True on successful recompilation, false otherwise.
   */
  bool RecompileHotBlock(uint64_t pc, int optimizationLevel);

  /**
   * @brief Checks if JIT compilation is available on the current platform.
   * @return True if JIT compilation is available, false otherwise.
   */
  bool IsAvailable() const;

private:
  /**
   * @brief Information about a compiled JIT block.
   */
  struct BlockInfo {
    uint8_t *code = nullptr;        ///< Pointer to compiled code
    uint64_t size = 0;              ///< Size of compiled code in bytes
    uint64_t lastUsed = 0;          ///< Last used cycle count for LRU tracking
    uint32_t branchCount = 0;       ///< Number of branches in the block
    uint32_t executionCount = 0;    ///< Number of times block was executed
    uint64_t totalCycles = 0;       ///< Total CPU cycles spent in this block
    bool isHotPath = false;         ///< Whether this block is a hot path
    uint64_t compilationTimeUs = 0; ///< Time taken to compile this block

    // Block characteristics
    uint32_t instructionCount = 0;     ///< Number of instructions in the block
    uint32_t simdInstructionCount = 0; ///< Number of SIMD instructions
    uint32_t memoryAccessCount = 0;    ///< Number of memory access instructions
    uint32_t conditionalBranches = 0;  ///< Number of conditional branches
    bool containsCall = false; ///< Whether block contains function calls
    bool containsIndirectJump = false; ///< Whether block has indirect jumps

    // Performance metrics
    float avgCyclesPerExecution = 0.0f; ///< Average cycles per execution
    uint32_t cacheMissCount = 0;    ///< Number of cache misses in this block
    uint32_t branchMispredicts = 0; ///< Number of branch mispredictions
    uint32_t tlbMissCount = 0;      ///< Number of TLB misses

    // Optimization info
    uint8_t optimizationLevel = 0;   ///< Current optimization level (0-3)
    bool isInlined = false;          ///< Whether block was inlined
    bool wasRecompiled = false;      ///< Whether block was recompiled
    uint32_t recompilationCount = 0; ///< Number of times block was recompiled

    // Dependencies and linking
    std::vector<uint64_t> predecessors;  ///< Addresses of predecessor blocks
    std::vector<uint64_t> successors;    ///< Addresses of successor blocks
    std::vector<uint64_t> inlinedBlocks; ///< Addresses of inlined blocks

    // Memory management
    uint64_t allocatedMemory = 0; ///< Total memory allocated for this block
    bool isRelocatable = true;    ///< Whether code is position-independent
    uint64_t alignmentRequirement = 0; ///< Required memory alignment

    // Debug info
    std::string debugAnnotation;   ///< Optional debug information
    uint64_t sourceAddress = 0;    ///< Original address in source binary
    uint32_t compilationFlags = 0; ///< Flags used during compilation

    // Profiling data
    struct ProfileData {
      uint64_t lastProfileTime = 0;        ///< Timestamp of last profiling
      float recentUsageFrequency = 0.0f;   ///< Recent execution frequency
      uint32_t optimizationPriority = 0;   ///< Priority for optimization
      bool markedForRecompilation = false; ///< Whether scheduled for recompile
    } profile;

    // Cache behavior
    struct CacheData {
      uint32_t evictionCount = 0;    ///< Times evicted from cache
      uint64_t lastEvictionTime = 0; ///< Last time block was evicted
      float cacheUtilization = 0.0f; ///< Cache utilization metric
      bool isPinned = false;         ///< Whether block is pinned in cache
    } cache;
  };

  /**
   * @brief Allocates executable memory for JIT code.
   * @param size Size of memory to allocate.
   * @return Pointer to allocated memory.
   */
  static uint8_t *AllocateExecutableMemory(size_t size);

  /**
   * @brief Frees executable memory.
   * @param addr Pointer to memory.
   * @param size Size of memory.
   */
  static void FreeExecutableMemory(uint8_t *addr, size_t size);

  /**
   * @brief Emits code to load an operand into a scratch register.
   * @param operand The operand to load.
   * @param scratchReg Scratch register index.
   * @param nextRip Next instruction pointer.
   * @param code Output code vector.
   */
  void EmitLoadOperand(const DecodedInstruction::Operand &operand,
                       uint8_t scratchReg, uint64_t nextRip,
                       std::vector<uint8_t> &code);

  /**
   * @brief Emits code to store a scratch register into an operand.
   * @param operand The operand to store.
   * @param scratchReg Scratch register index.
   * @param nextRip Next instruction pointer.
   * @param code Output code vector.
   */
  void EmitStoreOperand(const DecodedInstruction::Operand &operand,
                        uint8_t scratchReg, uint64_t nextRip,
                        std::vector<uint8_t> &code);

  /**
   * @brief Optimizes a compiled code block.
   * @param code Code vector to optimize.
   */
  void OptimizeBlock(std::vector<uint8_t> &code);

  /**
   * @brief Fixes up relative addresses in compiled code.
   * @param executableCode Pointer to executable memory.
   * @param codeSize Size of the code.
   */
  void FixupRelativeAddresses(uint8_t *executableCode, size_t codeSize);

  /**
   * @brief Allocates registers for a set of instructions.
   * @param instructions Instructions to process.
   */
  void AllocateRegisters(std::vector<DecodedInstruction> &instructions);
  /**
   * @brief Compiles a SIMD instruction.
   * @param instr The SIMD instruction.
   * @param code Output code vector.
   * @return True on success, false otherwise.
   */
  bool CompileSIMD(const DecodedInstruction &instr, std::vector<uint8_t> &code);

  // ENHANCEMENT: Advanced register allocation structures
  struct LiveRange {
    uint32_t start;      ///< Start instruction index
    uint32_t end;        ///< End instruction index
    Register virtualReg; ///< Virtual register
    uint8_t physicalReg; ///< Assigned physical register
    bool isSpilled;      ///< Whether the register is spilled
    uint32_t spillSlot;  ///< Spill slot index
    float priority;      ///< Allocation priority

    // NEW: Enhanced analysis fields
    uint32_t accessCount = 0;       ///< Number of times register is accessed
    uint32_t interferenceCount = 0; ///< Number of interfering ranges
    bool isDestination = false; ///< Whether register is a destination operand
  };

  struct RegisterState {
    bool isAvailable;   ///< Whether register is available
    uint32_t lastUsed;  ///< Last instruction that used this register
    Register boundVReg; ///< Bound virtual register
  };

  // ENHANCEMENT: Optimization pass structures
  enum class OptimizationLevel {
    None = 0,
    Basic = 1,
    Aggressive = 2,
    Maximum = 3
  };

  struct OptimizationPass {
    std::string name;
    std::function<void(std::vector<uint8_t> &)> pass;
    OptimizationLevel minLevel;
  };

  // ENHANCEMENT: Enhanced register allocation with live range analysis
  void
  PerformLiveRangeAnalysis(const std::vector<DecodedInstruction> &instructions,
                           std::vector<LiveRange> &liveRanges);
  bool LinearScanRegisterAllocation(std::vector<LiveRange> &liveRanges);
  void GenerateSpillCode(const LiveRange &range, std::vector<uint8_t> &code);

  // ENHANCEMENT: Advanced optimization passes
  void DeadCodeElimination(std::vector<uint8_t> &code);
  void CommonSubexpressionElimination(std::vector<uint8_t> &code);
  void ConstantFoldingAndPropagation(std::vector<uint8_t> &code);
  void InstructionCombining(std::vector<uint8_t> &code);
  void LoopOptimizations(std::vector<uint8_t> &code);
  void ControlFlowOptimizations(std::vector<uint8_t> &code);

  // ENHANCEMENT: Tiered compilation methods
  bool ShouldUseOptimization(uint64_t pc, OptimizationLevel level);
  OptimizationLevel DetermineOptimizationLevel(uint64_t pc);
  void ApplyOptimizationPasses(std::vector<uint8_t> &code,
                               OptimizationLevel level);

  // ENHANCEMENT: Comprehensive instruction emission methods
  void EmitInstruction(const DecodedInstruction &instr,
                       std::vector<uint8_t> &code);
  void EmitArithmeticInstruction(const DecodedInstruction &instr,
                                 std::vector<uint8_t> &code);
  void EmitMemoryInstruction(const DecodedInstruction &instr,
                             std::vector<uint8_t> &code);
  void EmitControlFlowInstruction(const DecodedInstruction &instr,
                                  std::vector<uint8_t> &code);
  void EmitSIMDInstruction(const DecodedInstruction &instr,
                           std::vector<uint8_t> &code);

  // NEW: Additional instruction category methods
  void EmitLogicalInstruction(const DecodedInstruction &instr,
                              std::vector<uint8_t> &code);
  void EmitBitwiseInstruction(const DecodedInstruction &instr,
                              std::vector<uint8_t> &code);
  void EmitStringInstruction(const DecodedInstruction &instr,
                             std::vector<uint8_t> &code);
  void EmitStackInstruction(const DecodedInstruction &instr,
                            std::vector<uint8_t> &code);
  void EmitCompareInstruction(const DecodedInstruction &instr,
                              std::vector<uint8_t> &code);
  void EmitShiftInstruction(const DecodedInstruction &instr,
                            std::vector<uint8_t> &code);
  void EmitConditionalInstruction(const DecodedInstruction &instr,
                                  std::vector<uint8_t> &code);
  void EmitSystemInstruction(const DecodedInstruction &instr,
                             std::vector<uint8_t> &code);

  // NEW: Enhanced SIMD instruction emission methods
  void EmitPackedArithmeticInstruction(const DecodedInstruction &instr,
                                       std::vector<uint8_t> &code);
  void EmitPackedLogicalInstruction(const DecodedInstruction &instr,
                                    std::vector<uint8_t> &code);
  void EmitPackedCompareInstruction(const DecodedInstruction &instr,
                                    std::vector<uint8_t> &code);
  void EmitPackedMoveInstruction(const DecodedInstruction &instr,
                                 std::vector<uint8_t> &code);
  void EmitPackedConversionInstruction(const DecodedInstruction &instr,
                                       std::vector<uint8_t> &code);
  void EmitAVXInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);

  // NEW: REX prefix and ModR/M encoding helpers
  void EmitREXPrefix(bool w, bool r, bool x, bool b,
                     std::vector<uint8_t> &code);
  void EmitVEXPrefix(uint8_t len, uint8_t pp, uint8_t mmmmm, bool w, bool r,
                     bool x, bool b, uint8_t vvvv, std::vector<uint8_t> &code);
  uint8_t EncodeModRM(uint8_t mod, uint8_t reg, uint8_t rm);
  uint8_t EncodeSIB(uint8_t scale, uint8_t index, uint8_t base);

  // NEW: Enhanced addressing mode and immediate helpers
  void EmitAddressingMode(const DecodedInstruction::Operand &operand,
                          uint8_t regField, std::vector<uint8_t> &code);
  void EmitImmediate(uint64_t immediate, uint8_t size,
                     std::vector<uint8_t> &code);

  // NEW: Instruction validation helpers
  bool ValidateInstructionOperands(const DecodedInstruction &instr);
  bool ValidateSIMDOperands(const DecodedInstruction &instr);
  bool ValidateRegisterRange(Register reg, bool isXMM = false);

  // NEW: Loop optimization structures
  struct LoopInfo {
    size_t start;
    size_t end;
    size_t size;
    size_t tripCount;
    bool isCountingLoop;
  };

  // NEW: Optimization helper methods
  size_t GetInstructionLength(const std::vector<uint8_t> &code, size_t offset);
  bool IsJumpInstruction(uint8_t opcode);

  // Constant folding helpers
  bool FoldArithmeticWithZero(std::vector<uint8_t> &code, size_t offset);
  bool FoldMultiplication(std::vector<uint8_t> &code, size_t offset);
  bool FoldLogicalOperations(std::vector<uint8_t> &code, size_t offset);
  bool PropagateConstants(std::vector<uint8_t> &code, size_t offset);
  bool FoldShiftOperations(std::vector<uint8_t> &code, size_t offset);

  // CSE helpers
  uint64_t HashInstruction(const std::vector<uint8_t> &code, size_t offset,
                           size_t length);
  bool IsLoadInstruction(const std::vector<uint8_t> &code, size_t offset);
  bool HasSideEffects(const std::vector<uint8_t> &code, size_t start,
                      size_t end);
  bool IsArithmeticInstruction(const std::vector<uint8_t> &code, size_t offset);
  bool ModifiesOperands(const std::vector<uint8_t> &code, size_t start,
                        size_t end);
  bool CanReplaceWithMove(const std::vector<uint8_t> &code, size_t current,
                          size_t previous);
  void ReplaceWithMove(std::vector<uint8_t> &code, size_t current,
                       size_t previous);
  void
  InvalidateAffectedEntries(std::unordered_map<uint64_t, size_t> &valueTable,
                            std::unordered_map<uint64_t, size_t> &lastSeen,
                            const std::vector<uint8_t> &code, size_t offset,
                            size_t length);

  // Instruction combining helpers
  bool CombineLeaInstructions(std::vector<uint8_t> &code, size_t offset);
  bool ReduceMultiplicationToShift(std::vector<uint8_t> &code, size_t offset);
  bool CombineArithmeticOperations(std::vector<uint8_t> &code, size_t offset);
  bool CombineMoveOperations(std::vector<uint8_t> &code, size_t offset);
  bool ReduceDivisionToShift(std::vector<uint8_t> &code, size_t offset);
  bool CombineCompareAndJump(std::vector<uint8_t> &code, size_t offset);
  bool ReplaceExpensiveOperations(std::vector<uint8_t> &code, size_t offset);

  // Loop optimization helpers
  std::vector<LoopInfo> DetectLoops(const std::vector<uint8_t> &code);
  bool ShouldUnrollLoop(const LoopInfo &loop);
  bool UnrollLoop(std::vector<uint8_t> &code, const LoopInfo &loop);
  size_t EstimateLoopTripCount(const std::vector<uint8_t> &code, size_t start,
                               size_t end);
  bool IsCountingLoop(const std::vector<uint8_t> &code, size_t start,
                      size_t end);
  bool PerformLoopStrengthReduction(std::vector<uint8_t> &code,
                                    const LoopInfo &loop);
  bool PerformLoopInvariantCodeMotion(std::vector<uint8_t> &code,
                                      const LoopInfo &loop);

  // Control flow optimization helpers
  bool OptimizeJumpChains(std::vector<uint8_t> &code, size_t offset);
  bool RemoveRedundantJumps(std::vector<uint8_t> &code, size_t offset);
  bool OptimizeBranchPrediction(std::vector<uint8_t> &code, size_t offset);
  bool OptimizeConditionalMoves(std::vector<uint8_t> &code, size_t offset);

  // FIX: Added declarations for Emit... functions
  void EmitMovInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);
  void EmitAddInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);
  void EmitSubInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);
  void EmitPushInstruction(const DecodedInstruction &instr,
                           std::vector<uint8_t> &code);
  void EmitPopInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);

  // Core Arithmetic Instructions
  void EmitMulInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);
  void EmitImulInstruction(const DecodedInstruction &instr,
                           std::vector<uint8_t> &code);
  void EmitDivInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);
  void EmitIdivInstruction(const DecodedInstruction &instr,
                           std::vector<uint8_t> &code);
  void EmitIncInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);
  void EmitDecInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);
  void EmitNegInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);
  void EmitCmpInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);
  void EmitAdcInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);
  void EmitSbbInstruction(const DecodedInstruction &instr,
                          std::vector<uint8_t> &code);

  X86_64CPU *m_cpu;                                         ///< Associated CPU
  std::unordered_map<uint64_t, BlockInfo> m_compiledBlocks; ///< Compiled blocks
  uint64_t m_totalCodeSize = 0;                             ///< Total code size
  static constexpr uint64_t MAX_CODE_SIZE = 32 * 1024 * 1024; ///< Max code size
  uint64_t m_cycleCount = 0;                                  ///< Cycle counter
  mutable std::mutex m_cacheMutex;                            ///< Cache mutex
  Stats m_stats;                           ///< JIT statistics
  bool m_tieredCompilationEnabled = false; ///< Tiered compilation flag
  bool m_simdOptimizationsEnabled = false; ///< SIMD optimizations flag

  // ENHANCEMENT: Advanced allocation and optimization state
  std::vector<RegisterState> m_registerStates; ///< Physical register states
  std::vector<OptimizationPass>
      m_optimizationPasses; ///< Available optimization passes
  static constexpr uint8_t NUM_PHYSICAL_REGS =
      16; ///< Number of x86_64 GP registers
};

} // namespace x86_64
